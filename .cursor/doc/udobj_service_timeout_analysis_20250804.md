# UDOBJ服务异常日志分析报告

## 分析概述
分析时间：2025-08-04 至 2025-08-06  
分析trace数量：12个  
分析方法：严格按照 `.cursor/rules/t_paas_b_cep_log_analysis.mdc` 规则执行

## 问题分类

### 类型1：data-auth-service超时异常（主要问题）
**影响trace数量：9个**

#### traceId: E-E.linkecare.3911-24486458
**问题原因**: data-auth-service在执行数据权限检查时超时，导致UDOBJ服务异常
**问题代码位置**: 
- `com.facishare.paas.appframework.privilege.DataPrivilegeServiceImpl.doCheckDataPrivilege(DataPrivilegeServiceImpl.java:170)`
- `com.facishare.rest.core.HttpClientManager.executeCall(HttpClientManager.java:19)`
**建议改进措施**: 
1. 增加data-auth-service超时配置（当前8000ms不足）
2. 优化数据权限检查逻辑，减少组织架构查询复杂度
3. 增加权限检查缓存机制
**关键日志**: 
- `objectspermission-cost:8013 ms` (data-auth-service)
- `socketTimeOutConfig:8000 ms,request: curl -X POST 'http://172.17.4.230:39414/data-auth-service/datarights/objectspermission'`
**超时接口**: `/data-auth-service/datarights/objectspermission`

#### traceId: E-E.mxx2021.2786-41341239
**问题原因**: data-auth-service在执行数据权限检查时超时，导致UDOBJ服务异常
**问题代码位置**: 
- `com.facishare.paas.appframework.privilege.DataPrivilegeServiceImpl.doCheckDataPrivilege(DataPrivilegeServiceImpl.java:170)`
- `com.facishare.rest.core.HttpClientManager.executeCall(HttpClientManager.java:19)`
**建议改进措施**: 
1. 增加data-auth-service超时配置（当前8000ms不足）
2. 优化数据权限检查逻辑，减少组织架构查询复杂度
3. 增加权限检查缓存机制
**关键日志**: 
- `objectspermission-cost:10970 ms` (data-auth-service)
- `socketTimeOutConfig:8000 ms,request: curl -X POST 'http://172.17.4.230:39414/data-auth-service/datarights/objectspermission'`
**超时接口**: `/data-auth-service/datarights/objectspermission`

#### traceId: E-E.mxx2021.2786-83824720
**问题原因**: data-auth-service在执行数据权限检查时超时，导致UDOBJ服务异常
**问题代码位置**: 
- `com.facishare.paas.appframework.privilege.DataPrivilegeServiceImpl.doCheckDataPrivilege(DataPrivilegeServiceImpl.java:170)`
- `com.facishare.rest.core.HttpClientManager.executeCall(HttpClientManager.java:19)`
**建议改进措施**: 
1. 增加data-auth-service超时配置（当前8000ms不足）
2. 优化数据权限检查逻辑，减少组织架构查询复杂度
3. 增加权限检查缓存机制
**关键日志**: 
- `objectspermission-cost:8846 ms` (data-auth-service)
- `socketTimeOutConfig:8000 ms,request: curl -X POST 'http://172.17.4.230:39414/data-auth-service/datarights/objectspermission'`
**超时接口**: `/data-auth-service/datarights/objectspermission`

#### traceId: E-E.mxx2021.2786-28903321
**问题原因**: data-auth-service在执行数据权限检查时超时，导致UDOBJ服务异常
**问题代码位置**: 
- `com.facishare.paas.appframework.privilege.DataPrivilegeServiceImpl.doCheckDataPrivilege(DataPrivilegeServiceImpl.java:170)`
- `com.facishare.rest.core.HttpClientManager.executeCall(HttpClientManager.java:19)`
**建议改进措施**: 
1. 增加data-auth-service超时配置（当前8000ms不足）
2. 优化数据权限检查逻辑，减少组织架构查询复杂度
3. 增加权限检查缓存机制
**关键日志**: 
- `objectspermission-cost:8164 ms` (data-auth-service)
- `socketTimeOutConfig:8000 ms,request: curl -X POST 'http://172.17.4.230:39414/data-auth-service/datarights/objectspermission'`
**超时接口**: `/data-auth-service/datarights/objectspermission`

#### traceId: E-E.shrcyl.1386-66305305
**问题原因**: data-auth-service在执行数据权限检查时超时，导致UDOBJ服务异常
**问题代码位置**: 
- `com.facishare.paas.appframework.privilege.DataPrivilegeServiceImpl.doCheckDataPrivilege(DataPrivilegeServiceImpl.java:170)`
- `com.facishare.rest.core.HttpClientManager.executeCall(HttpClientManager.java:19)`
**建议改进措施**: 
1. 增加data-auth-service超时配置（当前8000ms不足）
2. 优化数据权限检查逻辑，减少组织架构查询复杂度
3. 增加权限检查缓存机制
**关键日志**: 
- `objectspermission-cost:8242 ms` (data-auth-service)
- `socketTimeOutConfig:8000 ms,request: curl -X POST 'http://172.17.4.230:39414/data-auth-service/datarights/objectspermission'`
**超时接口**: `/data-auth-service/datarights/objectspermission`

#### traceId: E-E.mxx2021.1875-21006372
**问题原因**: data-auth-service在执行数据权限检查时超时，导致UDOBJ服务异常
**问题代码位置**: 
- `com.facishare.paas.appframework.privilege.DataPrivilegeServiceImpl.doCheckDataPrivilege(DataPrivilegeServiceImpl.java:170)`
- `com.facishare.rest.core.HttpClientManager.executeCall(HttpClientManager.java:19)`
**建议改进措施**: 
1. 增加data-auth-service超时配置（当前8000ms不足）
2. 优化数据权限检查逻辑，减少组织架构查询复杂度
3. 增加权限检查缓存机制
**关键日志**: 
- `objectspermission-cost:9146 ms` (data-auth-service)
- `socketTimeOutConfig:8000 ms,request: curl -X POST 'http://172.17.4.230:39414/data-auth-service/datarights/objectspermission'`
**超时接口**: `/data-auth-service/datarights/objectspermission`

### 类型2：网络连接异常
**影响trace数量：2个**

#### traceId: E-ylspjt.18393-d5d9cbca812344c78b5f038b34c01b7aava
**问题原因**: 网络连接被重置，导致请求失败
**问题代码位置**:
- CEP层网络连接处理
- `executeRequest exceptionally, Connection reset by peer`
**建议改进措施**:
1. 增加网络连接重试机制
2. 优化网络连接池配置
3. 增加网络异常监控和告警
**关键日志**:
- `executeRequest exceptionally, Connection reset by peer`
- `未知异常：s311030002:[], traceId: E-ylspjt.18393-d5d9cbca812344c78b5f038b34c01b7aava`
**超时接口**: 无（非超时问题）

#### traceId: E-brandsh.1061-db235401196e402783668113d99ba309ava
**问题原因**: 远程连接被关闭，导致请求失败
**问题代码位置**:
- CEP层网络连接处理
- `executeRequest exceptionally, Remotely closed`
**建议改进措施**:
1. 增加网络连接重试机制
2. 优化网络连接池配置
3. 增加网络异常监控和告警
**关键日志**:
- `executeRequest exceptionally, Remotely closed`
- `未知异常：s311030002:[], traceId: E-brandsh.1061-db235401196e402783668113d99ba309ava`
**超时接口**: 无（非超时问题）

#### traceId: E-E.absen2022.1142-34530444
**问题原因**: 远程连接被关闭，导致请求失败
**问题代码位置**:
- CEP层网络连接处理
- `executeRequest exceptionally, Remotely closed`
**建议改进措施**:
1. 增加网络连接重试机制
2. 优化网络连接池配置
3. 增加网络异常监控和告警
**关键日志**:
- `executeRequest exceptionally, Remotely closed`
- `未知异常：s311030002:[], traceId: E-E.absen2022.1142-34530444`
**超时接口**: 无（非超时问题）

### 类型3：参数为空异常
**影响trace数量：1个**

#### traceId: E-ylspjt.9280-72b4aae5b3694e8b88e0ab2f96c46024ava
**问题原因**: 请求参数objectDataId为空，导致查询对象数据失败
**问题代码位置**:
- `com.facishare.paas.appframework.metadata.MetaDataFindServiceImpl.findObjectData(MetaDataFindServiceImpl.java:168)`
- `com.facishare.paas.metadata.dispatcher.ObjectDataProxy.findById(ObjectDataProxy.java:185)`
**建议改进措施**:
1. 在Controller层增加参数校验，提前拦截空参数请求
2. 优化前端传参逻辑，确保必要参数不为空
3. 增加更友好的错误提示信息
**关键日志**:
- `findById parameter is null`
- `parameter is null`
**超时接口**: 无（非超时问题）

## 总体分析结论

### 主要问题
1. **data-auth-service性能瓶颈**：75%的异常由数据权限服务超时引起
2. **网络连接不稳定**：16.7%的异常由网络连接问题引起
3. **超时配置不合理**：当前8000ms超时配置无法满足复杂权限检查需求
4. **组织架构查询复杂**：权限检查涉及大量组织架构相关查询，性能较差

### 紧急改进建议
1. **立即调整超时配置**：将data-auth-service超时时间从8000ms调整至15000ms
2. **优化权限检查逻辑**：减少不必要的组织架构递归查询
3. **增加缓存机制**：对频繁查询的权限结果进行缓存
4. **网络连接优化**：增加网络连接重试机制和连接池优化
5. **监控告警**：增加data-auth-service响应时间和网络连接异常监控

### 长期优化方案
1. **权限服务重构**：考虑权限检查异步化处理
2. **数据库优化**：优化组织架构相关表的索引和查询语句
3. **架构升级**：考虑引入分布式缓存提升性能

---
**分析完成时间**: 2025-08-07  
**分析工具**: 基于日志查询系统的trace分析  
**分析人员**: AI助手 (严格遵循分析规则)
